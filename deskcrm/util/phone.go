package util

import (
	"crypto/md5"
	"deskcrm/api/dataproxy"
	"deskcrm/api/location"
	"fmt"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// maskPhone 电话号码脱敏
func MaskPhone(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	// 保留前3位和后4位，中间用*替换
	return phone[:3] + "****" + phone[len(phone)-4:]
}

// getMd5Phone 获取电话号码的MD5值
func GetMd5Phone(phone string) string {
	h := md5.New()
	h.Write([]byte(phone))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// getPhoneLocation 获取电话号码的城市信息
func GetPhoneLocation(ctx *gin.Context, phone string) (city string, cityLevel string) {
	if phone == "" {
		return "", ""
	}

	// 调用 Location API 获取电话号码的城市信息
	locationClient := location.NewClient()
	locationResp, err := locationClient.GetPhoneLocation(ctx, phone)
	if err != nil {
		zlog.Warnf(ctx, "getPhoneLocation failed, phone: %s, err: %v", phone, err)
		return "", ""
	}

	if locationResp == nil {
		return "", ""
	}

	// 如果有城市信息，通过 DataProxy 获取城市级别
	if locationResp.City != "" {
		cityNames := []string{locationResp.City}
		dataproxyClient := dataproxy.NewClient()
		cityDataList, err := dataproxyClient.GetEsCityData(ctx, cityNames)
		if err != nil {
			zlog.Warnf(ctx, "GetEsCityData failed, city: %s, err: %v", locationResp.City, err)
			return locationResp.City, ""
		}

		// 查找匹配的城市信息
		for _, cityData := range cityDataList {
			if cityData.CityName == locationResp.City {
				return cityData.CityName, cityData.CityLevelName
			}
		}
	}

	return locationResp.City, ""
}
